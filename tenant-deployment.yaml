apiVersion: apps/v1
kind: Deployment
metadata:
  name: lifecycle-final-1752247444-backend
  namespace: tenant-lifecycle-final-1752247444
  labels:
    app: lifecycle-final-1752247444-backend
    tenant: lifecycle-final-1752247444
    component: backend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: lifecycle-final-1752247444-backend
  template:
    metadata:
      labels:
        app: lifecycle-final-1752247444-backend
        tenant: lifecycle-final-1752247444
        component: backend
    spec:
      containers:
      - name: webapp
        image: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.56-test
        ports:
        - containerPort: 9000
        env:
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: db-credentials
              key: host
        - name: DB_PORT
          valueFrom:
            secretKeyRef:
              name: db-credentials
              key: port
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: db-credentials
              key: username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: db-credentials
              key: password
        - name: DB_NAME
          value: "architrave"
        - name: CUSTOMER_ID
          value: "lifecycle-final-1752247444"
        - name: CUSTOMER_NAME
          value: "Lifecycle Final Validation"
        - name: APP_ENVIRONMENT
          value: "production"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        readinessProbe:
          tcpSocket:
            port: 9000
          initialDelaySeconds: 30
          periodSeconds: 10
        livenessProbe:
          tcpSocket:
            port: 9000
          initialDelaySeconds: 60
          periodSeconds: 30
---
apiVersion: v1
kind: Service
metadata:
  name: lifecycle-final-1752247444-backend
  namespace: tenant-lifecycle-final-1752247444
  labels:
    app: lifecycle-final-1752247444-backend
    tenant: lifecycle-final-1752247444
spec:
  selector:
    app: lifecycle-final-1752247444-backend
  ports:
  - port: 9000
    targetPort: 9000
    protocol: TCP
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: lifecycle-final-1752247444-frontend
  namespace: tenant-lifecycle-final-1752247444
  labels:
    app: lifecycle-final-1752247444-frontend
    tenant: lifecycle-final-1752247444
    component: frontend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: lifecycle-final-1752247444-frontend
  template:
    metadata:
      labels:
        app: lifecycle-final-1752247444-frontend
        tenant: lifecycle-final-1752247444
        component: frontend
    spec:
      containers:
      - name: nginx
        image: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl
        ports:
        - containerPort: 80
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 10
          periodSeconds: 5
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: lifecycle-final-1752247444-frontend
  namespace: tenant-lifecycle-final-1752247444
  labels:
    app: lifecycle-final-1752247444-frontend
    tenant: lifecycle-final-1752247444
spec:
  selector:
    app: lifecycle-final-1752247444-frontend
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: lifecycle-final-1752247444-rabbitmq
  namespace: tenant-lifecycle-final-1752247444
  labels:
    app: lifecycle-final-1752247444-rabbitmq
    tenant: lifecycle-final-1752247444
    component: rabbitmq
spec:
  replicas: 1
  selector:
    matchLabels:
      app: lifecycle-final-1752247444-rabbitmq
  template:
    metadata:
      labels:
        app: lifecycle-final-1752247444-rabbitmq
        tenant: lifecycle-final-1752247444
        component: rabbitmq
    spec:
      containers:
      - name: rabbitmq
        image: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02
        ports:
        - containerPort: 5672
        - containerPort: 15672
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
        readinessProbe:
          tcpSocket:
            port: 5672
          initialDelaySeconds: 30
          periodSeconds: 10
        livenessProbe:
          tcpSocket:
            port: 5672
          initialDelaySeconds: 60
          periodSeconds: 30
---
apiVersion: v1
kind: Service
metadata:
  name: lifecycle-final-1752247444-rabbitmq
  namespace: tenant-lifecycle-final-1752247444
  labels:
    app: lifecycle-final-1752247444-rabbitmq
    tenant: lifecycle-final-1752247444
spec:
  selector:
    app: lifecycle-final-1752247444-rabbitmq
  ports:
  - name: amqp
    port: 5672
    targetPort: 5672
    protocol: TCP
  - name: management
    port: 15672
    targetPort: 15672
    protocol: TCP
  type: ClusterIP
